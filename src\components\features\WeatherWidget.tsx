
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Cloud, Sun, CloudRain, Wind, Thermometer, Droplets, Eye, RefreshCw } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { WeatherService, WeatherResponse } from '@/services/weatherService';

interface WeatherData {
  location: string;
  coordinates: { lat: number; lon: number };
  temperature: number;
  condition: string;
  description: string;
  humidity: number;
  windSpeed: number;
  visibility: number;
  recommendation: string;
  icon: string;
  forecast?: Array<{
    date: string;
    temp: number;
    condition: string;
    description: string;
    icon: string;
  }>;
}

const WeatherWidget = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [weather, setWeather] = useState<WeatherData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState<WeatherData | null>(null);



  useEffect(() => {
    fetchWeatherData();
  }, []);

  const fetchWeatherData = async () => {
    setLoading(true);
    try {
      const weatherResponses = await WeatherService.getSafariWeather();

      const weatherData = weatherResponses.map(response =>
        WeatherService.formatWeatherData(response)
      );

      setWeather(weatherData);
      setSelectedLocation(weatherData[0]);

      // toast({
      //   title: "Weather Updated",
      //   description: "Live weather data loaded successfully.",
      // });
    } catch (error) {
      console.error('Error fetching weather data:', error);
      toast({
        title: "Weather Data Error",
        description: "Unable to fetch current weather data. Please try again.",
        variant: "destructive"
      });

      // Fallback to mock data
      setWeather([
        {
          location: 'Serengeti National Park',
          coordinates: { lat: -2.3333, lon: 34.8333 },
          temperature: 28,
          condition: 'Clear',
          description: 'Clear skies',
          humidity: 45,
          windSpeed: 12,
          visibility: 10,
          recommendation: 'Perfect for game drives',
          icon: '//cdn.weatherapi.com/weather/64x64/day/113.png',
          forecast: []
        }
      ]);
      setSelectedLocation(weather[0] || null);
    } finally {
      setLoading(false);
    }
  };

  const getWeatherIcon = (data: WeatherData) => {
    // If we have a real icon URL from WeatherAPI, use it
    if (data.icon) {
      return (
        <img
          src={`https:${data.icon}`}
          alt={data.condition}
          className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0"
        />
      );
    }

    // Fallback to local icons based on condition text
    const iconSize = "h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0";
    const condition = data.condition.toLowerCase();
    if (condition.includes('sunny') || condition.includes('clear')) {
      return <Sun className={`${iconSize} text-yellow-500`} />;
    } else if (condition.includes('partly') || condition.includes('few')) {
      return <Cloud className={`${iconSize} text-gray-400`} />;
    } else if (condition.includes('cloudy') || condition.includes('overcast')) {
      return <Cloud className={`${iconSize} text-gray-500`} />;
    } else if (condition.includes('rain') || condition.includes('shower')) {
      return <CloudRain className={`${iconSize} text-blue-500`} />;
    } else {
      return <Sun className={`${iconSize} text-yellow-500`} />; // Default
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base sm:text-lg">
            <Wind className="h-5 w-5 mr-2 text-blue-600" />
            Live Weather Conditions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3 mb-2 sm:mb-0 flex-1">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-300 rounded-md flex-shrink-0"></div>
                  <div className="space-y-2 flex-1">
                    <div className="w-3/4 h-4 bg-gray-300 rounded"></div>
                    <div className="w-full h-3 bg-gray-300 rounded"></div>
                  </div>
                </div>
                <div className="w-16 h-8 bg-gray-300 rounded self-start sm:self-center ml-0 sm:ml-4"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center justify-between text-base sm:text-lg">
          <span className="flex items-center mb-2 sm:mb-0">
            <Wind className="h-5 w-5 mr-2 text-blue-600" />
            Live Weather Conditions
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchWeatherData}
            disabled={loading}
            className="self-start sm:self-auto"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              'Refresh'
            )}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {weather.map((data, index) => (
            <div 
              key={index} 
              className={`flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedLocation?.location === data.location ? 'bg-orange-50 border-orange-200' : 'hover:bg-gray-50'
              }`}
              onClick={() => setSelectedLocation(data)}
            >
              <div className="flex items-start sm:items-center space-x-3 mb-3 sm:mb-0 flex-1 min-w-0">
                {getWeatherIcon(data)}
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-sm sm:text-base truncate">{data.location}</h4>
                  <p className="text-xs sm:text-sm text-gray-600 truncate">{data.description}</p>
                  <p className="text-xs text-orange-600 mt-1 truncate">{data.recommendation}</p>
                </div>
              </div>
              <div className="text-left sm:text-right mt-2 sm:mt-0 flex-shrink-0 w-full sm:w-auto pl-0 sm:pl-4">
                <div className="text-xl sm:text-2xl font-bold">{data.temperature}°C</div>
                <div className="text-xs text-gray-500">
                  <div className="flex items-center space-x-2">
                    <span className="flex items-center">
                      <Droplets className="h-3 w-3 mr-1" />
                      {data.humidity}%
                    </span>
                    <span className="flex items-center">
                      <Wind className="h-3 w-3 mr-1" />
                      {data.windSpeed}km/h
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {selectedLocation && selectedLocation.forecast && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold mb-3 text-sm sm:text-base">5-Day Forecast for {selectedLocation.location}</h4>
            <div className="overflow-x-auto pb-2">
              <div className="grid grid-flow-col auto-cols-max gap-2 sm:grid-cols-5 sm:grid-flow-row">
                {selectedLocation.forecast.map((day, index) => (
                  <div key={index} className="text-center p-2 bg-white rounded min-w-[4.5rem] sm:min-w-0">
                    <div className="text-xs text-gray-600 mb-1">{day.date}</div>
                    <div className="flex justify-center mb-1">
                      {day.icon ? (
                        <img
                          src={`https:${day.icon}`}
                          alt={day.condition}
                          className="h-6 w-6"
                        />
                      ) : (
                        getWeatherIcon({ ...selectedLocation, condition: day.condition, icon: '' })
                      )}
                    </div>
                    <div className="text-sm font-semibold">{day.temp}°C</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className="mt-4 flex flex-col sm:flex-row gap-2">
          <Button className="flex-1 text-xs sm:text-sm" variant="outline">
            Weather-Based Tour Recommendations
          </Button>
          <Button className="flex-1 text-xs sm:text-sm" variant="outline">
            Safari Weather Guide
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default WeatherWidget;
